version: 5
dev:
    enablePreviewV2: true
    enableSelfContainedVaults: true
commands:
    lookup:
        note:
            selectionMode: extract
            confirmVaultOnCreate: true
            vaultSelectionModeOnCreate: smart
            leaveTrace: false
            bubbleUpCreateNew: true
            fuzzThreshold: 0.2
    randomNote: {}
    insertNoteLink:
        aliasMode: none
        enableMultiSelect: false
    insertNoteIndex:
        enableMarker: false
    copyNoteLink:
        aliasMode: title
    templateHierarchy: template
workspace:
    vaults:
        # -
        #     fsPath: dependencies/github.com/yourusername/project1-name
        #     remote:
        #         type: git
        #         url: https://github.com/yourusername/project1-name.git
        #     name: project1-name
        -
            fsPath: dependencies/github.com/jamesmiller2404/Voyage_to_Kah_Lon_7.2025
            remote:
                type: git
                url: https://github.com/jamesmiller2404/Voyage_to_Kah_Lon_7.2025.git
            name: Voyage_to_Kah_Lon_7.2025
        -
            fsPath: dependencies/github.com/jamesmiller2404/Binary_Bitch_Story
            remote:
                type: git
                url: https://github.com/jamesmiller2404/Binary_Bitch_Story.git
            name: Binary_Bitch_Story
        -
            fsPath: .
            selfContained: true
            name: Writing_Repo_for_VS-CODE_3
    journal:
        dailyDomain: daily
        name: journal
        dateFormat: y.MM.dd
        addBehavior: childOfDomain
    scratch:
        name: scratch
        dateFormat: y.MM.dd.HHmmss
        addBehavior: asOwnDomain
    task:
        name: task
        dateFormat: y.MM.dd
        addBehavior: asOwnDomain
        statusSymbols:
            '': ' '
            wip: w
            done: x
            assigned: a
            moved: m
            blocked: b
            delegated: l
            dropped: d
            pending: 'y'
        taskCompleteStatus:
            - done
            - x
        prioritySymbols:
            H: high
            M: medium
            L: low
        todoIntegration: false
        createTaskSelectionType: selection2link
    graph:
        zoomSpeed: 1
        createStub: false
    enableAutoCreateOnDefinition: false
    enableXVaultWikiLink: false
    enableRemoteVaultInit: true
    enableUserTags: true
    enableHashTags: true
    workspaceVaultSyncMode: noCommit
    enableAutoFoldFrontmatter: false
    enableEditorDecorations: true
    maxPreviewsCached: 10
    maxNoteLength: 204800
    enableFullHierarchyNoteTitle: false
preview:
    enableFMTitle: true
    enableNoteTitleForLink: true
    enableFrontmatterTags: true
    enableHashesForFMTags: false
    enablePrettyRefs: true
    enableKatex: true
    automaticallyShowPreview: false
publishing:
    enableFMTitle: true
    enableNoteTitleForLink: true
    enablePrettyRefs: true
    enableKatex: true
    copyAssets: true
    siteHierarchies:
        - root
    writeStubs: false
    siteRootDir: docs
    seo:
        title: Dendron
        description: Personal Knowledge Space
    github:
        enableEditLink: true
        editLinkText: Edit this page on GitHub
        editBranch: main
        editViewMode: tree
    enableSiteLastModified: true
    enableFrontmatterTags: true
    enableHashesForFMTags: false
    enableRandomlyColoredTags: true
    enableTaskNotes: true
    enablePrettyLinks: true
    searchMode: search
    duplicateNoteBehavior:
        action: useVault
        payload:
            - Binary_Bitch_Story
            - Writing_Repo_for_VS-CODE_3


#!/bin/bash

# Navigate to the repository directory
# Ensure this path is correct for your system
cd C:/Users/<USER>/Desktop/Writing_Repo_for_VS-CODE_3/dependencies/github.com/jamesmiller2404/Binary_Bitch_Story

# Check if the directory change was successful
if [ $? -ne 0 ]; then
  echo "Error: Could not change to the specified directory. Please check the path."
  exit 1
fi

echo "Successfully navigated to the repository."

# Pull the latest changes from the remote 'main' branch
# This helps prevent merge conflicts by ensuring your local branch is up-to-date
echo "Pulling latest changes from origin/main..."
git pull origin main

# Check if the pull was successful
if [ $? -ne 0 ]; then
  echo "Error: Git pull failed. Please resolve any conflicts or issues."
  exit 1
fi

# Add all changes in the current directory and subdirectories to the staging area
echo "Adding all changes to staging area..."
git add .

# Prompt the user for a commit message
read -p "Enter your commit message: " commit_message

# Check if the commit message is empty
if [ -z "$commit_message" ]; then
  echo "Commit message cannot be empty. Aborting commit."
  exit 1
fi

# Commit the staged changes with the provided message
echo "Committing changes with message: \"$commit_message\"..."
git commit -m "$commit_message"

# Check if the commit was successful
if [ $? -ne 0 ]; then
  echo "Error: Git commit failed. There might be no changes to commit or other issues."
  exit 1
fi

# Push the committed changes to the 'main' branch on the 'origin' remote
echo "Pushing changes to origin/main..."
git push origin main

# Check if the push was successful
if [ $? -ne 0 ]; then
  echo "Error: Git push failed. Please check your remote connection or permissions."
  exit 1
fi

echo "Script completed successfully! Your changes have been pushed."
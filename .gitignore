# Dendron specific generated files
.dendron.cache.json
.dendron.history.json
.dendron.log
.dendron.port
.dendron.ws

# Standard development ignorable files
node_modules/
build/

# Ignore the actual vault folders, as they are separate Git repositories.
# These paths MUST match the 'fsPath' for your 'remote' vaults in dendron.yml.
dependencies/github.com/jamesmiller2404/Binary_Bitch_Story/
dependencies/github.com/jamesmiller2404/Voyage_to_Kah_Lon_7.2025/

# Example: If you have a specific file that is generated, add it here:
# my_generated_file.txt
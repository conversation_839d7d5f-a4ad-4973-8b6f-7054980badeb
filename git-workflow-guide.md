# Git Workflow Guide for Writing Projects

## Overview
This guide covers Git workflows for managing multiple writing projects in your Dendron workspace. Each project is a separate remote repository, giving you full control over commits and version history.

## Daily Writing Session Workflow

### Start of Session
```bash
# Pull latest changes (if collaborating or working from multiple devices)
cd dependencies/github.com/jamesmiller2404/Binary_Bitch_Story
git pull origin main
```

### End of Session
```bash
# Check what you've changed
git status
git diff

# Commit your work
git add .
git commit -m "Chapter 3: Added confrontation scene (1,200 words)"
git push origin main
```

## Meaningful Commit Messages for Writers

### Good Examples
```bash
git commit -m "Chapter 5: First draft complete"
git commit -m "Characters: Developed <PERSON>'s backstory"
git commit -m "Plot: Restructured Act 2 climax"
git commit -m "Editing: Fixed plot holes in chapters 7-9"
git commit -m "Research: Added medieval weapons notes"
```

### Avoid These
```bash
git commit -m "updates"
git commit -m "changes"
git commit -m "wip"
```

## Branch Strategy for Major Revisions

### For Big Changes (like restructuring a story)
```bash
# Create a new branch for major revision
git checkout -b revision-act2-restructure

# Make your changes...
# Write, edit, experiment freely

# When satisfied, merge back
git checkout main
git merge revision-act2-restructure
git push origin main

# Clean up
git branch -d revision-act2-restructure
```

## Quick Commands for Daily Use

### Make Scripts Executable
```bash
chmod +x check-all-projects.sh backup-all-projects.sh
```

### Daily Workflow
```bash
# Morning: Check status of all projects
./check-all-projects.sh

# During writing: Work normally in Dendron

# Evening: Backup everything
./backup-all-projects.sh
```

## Individual Project Commands

### Quick Project Navigation
Add these aliases to your shell profile (.bashrc or .zshrc):
```bash
alias goto-binary="cd dependencies/github.com/jamesmiller2404/Binary_Bitch_Story"
alias goto-project2="cd dependencies/github.com/jamesmiller2404/Next_Project"
```

### Project-Specific Commits
```bash
goto-binary
git add chapters/chapter-05.md
git commit -m "Chapter 5: Added twist reveal scene"
git push origin main
```

## Emergency Recovery Tips

### If You Accidentally Delete Something
```bash
# See recent commits
git log --oneline -10

# Restore a file from previous commit
git checkout HEAD~1 -- chapters/chapter-03.md

# Undo last commit (keeps changes)
git reset --soft HEAD~1
```

### View File History
```bash
# See all changes to a specific file
git log --follow -p chapters/chapter-01.md

# See who changed what and when
git blame chapters/chapter-01.md
```

## Recommended Daily Routine

1. **Start:** `./check-all-projects.sh`
2. **Write:** Focus on your story in Dendron
3. **Save points:** Commit major milestones manually
4. **End:** `./backup-all-projects.sh`

## Advanced Tips

### Selective Commits
```bash
# Add only specific files
git add chapters/chapter-03.md characters/protagonist.md
git commit -m "Chapter 3 and character development"

# Interactive staging (choose which changes to include)
git add -p
```

### Working with Multiple Devices
```bash
# Before starting work on a new device
git pull origin main

# After finishing work
git push origin main
```

### Collaboration
```bash
# If working with an editor or co-author
git pull origin main  # Get their changes
# Make your changes
git add .
git commit -m "Your changes"
git push origin main  # Share your changes
```

## Troubleshooting

### Merge Conflicts
```bash
# If git pull shows conflicts
git status  # See which files have conflicts
# Edit files to resolve conflicts (look for <<<< ==== >>>> markers)
git add .
git commit -m "Resolved merge conflicts"
```

### Forgot to Pull Before Making Changes
```bash
git stash  # Save your changes temporarily
git pull origin main  # Get remote changes
git stash pop  # Restore your changes
# Resolve any conflicts if they occur
```

This workflow gives you complete control over your writing projects while maintaining clean, meaningful version history for each story!

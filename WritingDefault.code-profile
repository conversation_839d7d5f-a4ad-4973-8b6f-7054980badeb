{"name": "<PERSON><PERSON><PERSON> (Copy)", "settings": "{\"settings\":\"{\\r\\n    \\\"vscode-openai.serviceProvider\\\": \\\"OpenAI\\\",\\r\\n    \\\"vscode-openai.baseUrl\\\": \\\"https://api.openai.com/v1\\\",\\r\\n    \\\"vscode-openai.defaultModel\\\": \\\"gpt-3.5-turbo\\\",\\r\\n    \\\"vscode-openai.embeddingModel\\\": \\\"text-embedding-ada-002\\\",\\r\\n    \\\"vscode-openai.azureDeployment\\\": \\\"setup-required\\\",\\r\\n    \\\"vscode-openai.embeddingsDeployment\\\": \\\"setup-required\\\",\\r\\n    \\\"vscode-openai.azureApiVersion\\\": \\\"2023-05-15\\\",\\r\\n    \\\"workbench.editor.empty.hint\\\": \\\"hidden\\\",\\r\\n    \\\"git.openRepositoryInParentFolders\\\": \\\"never\\\",\\r\\n    \\\"workbench.editorAssociations\\\": {\\r\\n        \\\"*.docx\\\": \\\"default\\\"\\r\\n    },\\r\\n    \\\"workbench.colorTheme\\\": \\\"Visual Studio Dark\\\",\\r\\n    \\\"github.copilot.editor.enableAutoCompletions\\\": true,\\r\\n    \\\"amazonQ.suppressPrompts\\\": {\\r\\n        \\\"codeWhispererConnectionExpired\\\": true\\r\\n    },\\r\\n    \\\"workbench.editor.enablePreview\\\": false,\\r\\n    \\\"editor.minimap.enabled\\\": false,\\r\\n    \\\"editor.stickyScroll.enabled\\\": false,\\r\\n    \\\"continue.telemetryEnabled\\\": false,\\r\\n    \\\"gitlens.ai.model\\\": \\\"anthropic:claude-3-5-sonnet-latest\\\",\\r\\n    \\\"github.copilot.advanced\\\": {\\r\\n        \\\"authProvider\\\": \\\"github\\\"\\r\\n    },\\r\\n    \\\"terminal.integrated.defaultProfile.windows\\\": \\\"Git Bash\\\",\\r\\n    \\\"geminicodeassist.project\\\": \\\"\\\",\\r\\n    \\\"print.folder.fileNames\\\": true,\\r\\n    \\\"print.folder.include\\\": [],\\r\\n    \\\"print.folder.exclude\\\": [\\r\\n        \\\"{bin,obj,out}\\\",\\r\\n        \\\"node_modules\\\",\\r\\n        \\\"data\\\",\\r\\n        \\\"**/*.{bin,exe,dll,hex,pdb,pdf,pfx,png,jpg,gif,bmp,suo,design}\\\"\\r\\n    ],\\r\\n    \\\"print.folder.maxLines\\\": 1200,\\r\\n    \\\"print.folder.maxFiles\\\": 30,\\r\\n    \\\"print.folder.includeFileList\\\": false,\\r\\n    \\\"github.copilot.enable\\\": {\\r\\n        \\\"*\\\": false\\r\\n    },\\r\\n\\r\\n    \\\"[markdown]\\\": {\\r\\n    \\\"editor.quickSuggestions\\\": {\\r\\n        \\\"comments\\\": \\\"off\\\",\\r\\n        \\\"strings\\\": \\\"off\\\",\\r\\n        \\\"other\\\": \\\"off\\\"\\r\\n    },\\r\\n    \\\"editor.codeActionsOnSave\\\": {},\\r\\n    \\\"editor.formatOnSave\\\": false\\r\\n    },\\r\\n    \\\"git.autofetch\\\": true,\\r\\n    \\\"diffEditor.codeLens\\\": true\\r\\n}\"}", "extensions": "[{\"identifier\":{\"id\":\"github.copilot\",\"uuid\":\"23c4aeee-f844-43cd-b53e-1113e483f1a6\"},\"displayName\":\"GitHub Copilot\",\"applicationScoped\":true},{\"identifier\":{\"id\":\"rassek96.vscode-comment-selection\",\"uuid\":\"962f3026-e8cd-40bf-a02e-699ce95734b1\"},\"displayName\":\"vscode-comment-selection\",\"applicationScoped\":true}]", "globalState": "{\"storage\":{\"workbench.panel.markers.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.markers.view\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.output.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.output\\\",\\\"isHidden\\\":false}]\",\"terminal.hidden\":\"[{\\\"id\\\":\\\"terminal\\\",\\\"isHidden\\\":false}]\",\"workbench.explorer.views.state.hidden\":\"[{\\\"id\\\":\\\"outline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"timeline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.explorer.openEditorsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.explorer.emptyView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.explorer.fileView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"npm\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"aws.appBuilderForFileExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"aws.amazonq.notifications\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"aws.amazonq.AmazonCommonAuth\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"aws.amazonq.SecurityIssuesTree\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"aws.AmazonQChatView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"aws.codeWhisperer.referenceLog\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dendron.sample\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"claude-dev.SidebarProvider\\\",\\\"isHidden\\\":false}]\",\"workbench.scm.views.state.hidden\":\"[{\\\"id\\\":\\\"workbench.scm.repositories\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.scm\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.scm.sync\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.scm.history\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.repositories\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.commits\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.branches\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.remotes\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.stashes\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.tags\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.worktrees\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.contributors\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.scm.grouped\\\",\\\"isHidden\\\":false}]\",\"workbench.view.search.state.hidden\":\"[{\\\"id\\\":\\\"workbench.view.search\\\",\\\"isHidden\\\":false}]\",\"workbench.activity.pinnedViewlets2\":\"[{\\\"id\\\":\\\"workbench.view.search\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":1},{\\\"id\\\":\\\"workbench.view.explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":0},{\\\"id\\\":\\\"workbench.view.scm\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":2},{\\\"id\\\":\\\"workbench.view.debug\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":3},{\\\"id\\\":\\\"workbench.view.extensions\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":4},{\\\"id\\\":\\\"workbench.view.remote\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":4},{\\\"id\\\":\\\"workbench.view.extension.test\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":6},{\\\"id\\\":\\\"workbench.view.extension.references-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":7},{\\\"id\\\":\\\"workbench.view.extension.copilot-chat\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":14},{\\\"id\\\":\\\"workbench.view.extension.gitlens\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.geminiChat\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":15},{\\\"id\\\":\\\"workbench.view.extension.gitlensInspect\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":12},{\\\"id\\\":\\\"workbench.view.extension.makefile__viewContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":18},{\\\"id\\\":\\\"workbench.view.extension.vscode-openai-sidebar-view\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.dockerView\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":16},{\\\"id\\\":\\\"workbench.view.extension.kubernetesView\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":16},{\\\"id\\\":\\\"workbench.view.extension.1-geminiAIChatViewContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":14},{\\\"id\\\":\\\"workbench.view.extension.1-cloudCodeContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":16},{\\\"id\\\":\\\"workbench.view.extension.1-cloudCodeDuetAIChatViewContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.aws-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.gitlensPatch\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":13},{\\\"id\\\":\\\"workbench.view.extension.jupyter\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":17},{\\\"id\\\":\\\"workbench.view.extension.containersView\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":15},{\\\"id\\\":\\\"workbench.view.extension.cspell-info-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":19},{\\\"id\\\":\\\"workbench.view.extension.dendron-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.cspell-regexp-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":20},{\\\"id\\\":\\\"workbench.view.extension.augment-chat\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.panel.chatSidebar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":100},{\\\"id\\\":\\\"workbench.panel.chatSidebar.copilot\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":100},{\\\"id\\\":\\\"userDataProfiles\\\",\\\"pinned\\\":true,\\\"visible\\\":false},{\\\"id\\\":\\\"workbench.view.sync\\\",\\\"pinned\\\":true,\\\"visible\\\":false},{\\\"id\\\":\\\"workbench.view.editSessions\\\",\\\"pinned\\\":true,\\\"visible\\\":false}]\",\"workbench.view.debug.state.hidden\":\"[{\\\"id\\\":\\\"workbench.debug.welcome\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.variablesView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.watchExpressionsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.callStackView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.loadedScriptsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.breakPointsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jsBrowserBreakpoints\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jsExcludedCallers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jsDebugNetworkTree\\\",\\\"isHidden\\\":false}]\",\"workbench.telemetryOptOutShown\":\"true\",\"memento/gettingStartedService\":\"{\\\"pickColorTheme\\\":{\\\"done\\\":true},\\\"ms-python.python#pythonWelcome2#python.createPythonFolder\\\":{\\\"done\\\":true},\\\"pickAFolderTask-Other\\\":{\\\"done\\\":true},\\\"ms-python.python#pythonDataScienceWelcome#python.createNewNotebook\\\":{\\\"done\\\":true},\\\"ms-python.python#pythonWelcome#python.createPythonFile\\\":{\\\"done\\\":true},\\\"ms-python.python#pythonWelcome2#python.createPythonFile\\\":{\\\"done\\\":true},\\\"commandPaletteTask\\\":{\\\"done\\\":true},\\\"commandPaletteTaskWeb\\\":{\\\"done\\\":true},\\\"ms-python.python#pythonWelcome#python.createEnvironment\\\":{\\\"done\\\":true},\\\"ms-python.python#pythonWelcome2#python.createEnvironment2\\\":{\\\"done\\\":true},\\\"installGit\\\":{\\\"done\\\":true},\\\"ms-vscode-remote.remote-wsl#wslWalkthrough#create.project\\\":{\\\"done\\\":true},\\\"googlecloudtools.cloudcode#duet-ai#connect-signed-out\\\":{\\\"done\\\":true},\\\"googlecloudtools.cloudcode#duet-ai#project-signed-out\\\":{\\\"done\\\":true},\\\"ms-azuretools.vscode-docker#dockerStart#openFolder\\\":{\\\"done\\\":true},\\\"ms-vscode-remote.remote-wsl#wslWalkthrough#explore.commands\\\":{\\\"done\\\":true},\\\"pickAFolderTask-WebWeb\\\":{\\\"done\\\":true},\\\"ms-vscode.remote-repositories#remoteRepositoriesWalkthrough#openRepo\\\":{\\\"done\\\":true},\\\"ms-python.python#pythonWelcome#python.createPythonFolder\\\":{\\\"done\\\":true},\\\"amazonwebservices.amazon-q-vscode#aws.amazonq.walkthrough#aws.amazonq.walkthrough.chat\\\":{\\\"done\\\":true},\\\"commandPaletteTaskAccessibility\\\":{\\\"done\\\":true},\\\"ms-vscode-remote.remote-wsl#wslWalkthrough#install.tools\\\":{\\\"done\\\":true},\\\"quickOpen\\\":{\\\"done\\\":true},\\\"quickOpenWeb\\\":{\\\"done\\\":true},\\\"pickColorThemeWeb\\\":{\\\"done\\\":true},\\\"eamodio.gitlens#welcome#improve-workflows-with-integrations\\\":{\\\"done\\\":true},\\\"eamodio.gitlens#welcome#get-started-community\\\":{\\\"done\\\":true},\\\"eamodio.gitlens#welcome#welcome-in-trial\\\":{\\\"done\\\":true},\\\"eamodio.gitlens#welcome#welcome-in-trial-expired\\\":{\\\"done\\\":true},\\\"eamodio.gitlens#welcome#welcome-in-trial-expired-eligible\\\":{\\\"done\\\":true},\\\"eamodio.gitlens#welcome#welcome-paid\\\":{\\\"done\\\":true},\\\"settings\\\":{\\\"done\\\":true},\\\"CopilotSetupComplete\\\":{\\\"done\\\":true},\\\"GitHub.copilot-chat#copilotWelcome#copilot.panelChat\\\":{\\\"done\\\":true},\\\"settingsSync\\\":{\\\"done\\\":true},\\\"settingsAndSync\\\":{\\\"done\\\":true},\\\"settingsSyncWeb\\\":{\\\"done\\\":true},\\\"pdconsec.vscode-print#how-to-print#print-editor-active\\\":{\\\"done\\\":true},\\\"eamodio.gitlens#welcome#welcome-home-view\\\":{\\\"done\\\":true},\\\"eamodio.gitlens#welcome#visualize-code-history\\\":{\\\"done\\\":true},\\\"scmSetup\\\":{\\\"done\\\":true},\\\"terminal\\\":{\\\"done\\\":true},\\\"scmClone\\\":{\\\"done\\\":true},\\\"newCommandPaletteTask\\\":{\\\"done\\\":true},\\\"ms-toolsai.jupyter#jupyterWelcome#ipynb.newUntitledIpynb\\\":{\\\"done\\\":true},\\\"ms-azuretools.vscode-containers#containersStart#openFolder\\\":{\\\"done\\\":true},\\\"newPickColorTheme\\\":{\\\"done\\\":true}}\",\"workbench.statusbar.hidden\":\"[\\\"status.workspaceTrust.1693161541189\\\",\\\"status.workspaceTrust.a3f41856eb9527957876aea679c61394\\\",\\\"status.workspaceTrust.1693692872198\\\",\\\"status.workspaceTrust.64ced963f1cb10dbf3edea200e88fa20\\\",\\\"status.workspaceTrust.43cdf5739ea42bfe02f896397ccd6d22\\\",\\\"status.workspaceTrust.1694754926812\\\",\\\"status.workspaceTrust.1694754936790\\\",\\\"status.workspaceTrust.764473e765d96d65058f5f3387b44967\\\",\\\"status.workspaceTrust.35f50400ac37a251f030d717da8014d8\\\",\\\"status.workspaceTrust.1703032573155\\\",\\\"status.workspaceTrust.82a4ceceb9860fc282148942f9800c7c\\\",\\\"status.workspaceTrust.1703828588705\\\",\\\"status.workspaceTrust.b13ef1fcac9a7fc6bc3dada6bc509377\\\",\\\"status.workspaceTrust.1703978608611\\\",\\\"status.workspaceTrust.1704257037464\\\",\\\"status.workspaceTrust.1704358413517\\\",\\\"status.workspaceTrust.1704617635025\\\",\\\"status.workspaceTrust.1705466589627\\\",\\\"status.workspaceTrust.158de57e58aa3c4f5d7a8f75d5c2bf43\\\",\\\"status.workspaceTrust.276d6d29d0f4b680ffadd9e24ccb249d\\\",\\\"status.workspaceTrust.1706143846970\\\",\\\"status.workspaceTrust.1706168814660\\\",\\\"status.workspaceTrust.ad24970a8e57064c428aefdcd65c178d\\\",\\\"status.workspaceTrust.10b2d7a1bea2d80d26c5d34e7399205f\\\",\\\"status.workspaceTrust.561255e26561e955c4d2f8cd3b5025d2\\\",\\\"GitHub.copilot.status\\\"]\",\"themeUpdatedNotificationShown\":\"true\",\"workbench.view.extensions.state.hidden\":\"[{\\\"id\\\":\\\"workbench.views.extensions.installed\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchOutdated\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.workspaceRecommendations\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.popular\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchRecentlyUpdated\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.otherRecommendations\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"extensions.recommendedList\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.enabled\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.extensions.disabled\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.extensions.marketplace\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchInstalled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchEnabled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchDisabled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchBuiltin\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchWorkspaceUnsupported\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinFeatureExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinThemeExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinProgrammingLanguageExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.untrustedUnsupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.untrustedPartiallySupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.virtualUnsupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.virtualPartiallySupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.deprecatedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.mcp.installed\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.mcp.default.marketplace\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.mcp.marketplace\\\",\\\"isHidden\\\":false}]\",\"fileBasedRecommendations/promptedRecommendations\":\"{\\\"python\\\":[\\\"ms-python.python\\\"],\\\"makefile\\\":[\\\"ms-vscode.makefile-tools\\\"],\\\"plaintext\\\":[\\\"mechatroner.rainbow-csv\\\",\\\"tomoki1207.pdf\\\"]}\",\"workbench.welcomePage.walkthroughMetadata\":\"[[\\\"ms-python.python#pythonWelcome\\\",{\\\"firstSeen\\\":1693161597835,\\\"stepIDs\\\":[\\\"python.createPythonFile\\\",\\\"python.installPythonWin8\\\",\\\"python.installPythonMac\\\",\\\"python.installPythonLinux\\\",\\\"python.selectInterpreter\\\",\\\"python.createEnvironment\\\",\\\"python.runAndDebug\\\",\\\"python.learnMoreWithDS\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-python.python#pythonWelcome2\\\",{\\\"firstSeen\\\":1693161597836,\\\"stepIDs\\\":[\\\"python.createPythonFolder\\\",\\\"python.createPythonFile\\\",\\\"python.installPythonWin8\\\",\\\"python.installPythonMac\\\",\\\"python.installPythonLinux\\\",\\\"python.createEnvironment2\\\",\\\"python.runAndDebug\\\",\\\"python.learnMoreWithDS2\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-python.python#pythonDataScienceWelcome\\\",{\\\"firstSeen\\\":1693161597836,\\\"stepIDs\\\":[\\\"python.installJupyterExt\\\",\\\"python.createNewNotebook\\\",\\\"python.openInteractiveWindow\\\",\\\"python.dataScienceLearnMore\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-vscode-remote.remote-wsl#wslWalkthrough\\\",{\\\"firstSeen\\\":1694754927901,\\\"stepIDs\\\":[\\\"explore.commands\\\",\\\"open.wslwindow\\\",\\\"create.project\\\",\\\"open.project\\\",\\\"linux.environment\\\",\\\"install.tools\\\",\\\"run.debug\\\",\\\"come.back\\\"],\\\"manaullyOpened\\\":false}],[\\\"GitHub.copilot#copilotWelcome\\\",{\\\"firstSeen\\\":1702105753042,\\\"stepIDs\\\":[\\\"copilot.signin\\\",\\\"copilot.firstsuggest\\\",\\\"copilot.chat\\\",\\\"copilot.realfiles.openrecent\\\",\\\"copilot.realfiles.quickopen\\\",\\\"copilot.iterate\\\",\\\"copilot.creativity\\\"],\\\"manaullyOpened\\\":false}],[\\\"googlecloudtools.cloudcode#one-activity-bar\\\",{\\\"firstSeen\\\":1704144164497,\\\"stepIDs\\\":[\\\"one_activity_bar\\\",\\\"project_selector\\\",\\\"hide_show_view\\\",\\\"rearrange_view\\\",\\\"split_explorers\\\"],\\\"manaullyOpened\\\":false}],[\\\"googlecloudtools.cloudcode#duet-ai\\\",{\\\"firstSeen\\\":1704144164497,\\\"stepIDs\\\":[\\\"connect-signed-out\\\",\\\"connect-signed-in\\\",\\\"project-signed-out\\\",\\\"project-signed-in\\\",\\\"generation\\\",\\\"chat\\\"],\\\"manaullyOpened\\\":false}],[\\\"googlecloudtools.cloudcode#apigee-duet-ai\\\",{\\\"firstSeen\\\":1704144164497,\\\"stepIDs\\\":[\\\"magic_wand_button\\\",\\\"enter_prompt\\\",\\\"api_design_experience\\\",\\\"quick_actions\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-azuretools.vscode-docker#dockerStart\\\",{\\\"firstSeen\\\":1704257038631,\\\"stepIDs\\\":[\\\"openFolder\\\",\\\"openFolderMac\\\",\\\"scaffold\\\",\\\"buildImage\\\",\\\"runContainer\\\",\\\"dockerExplorer\\\",\\\"pushImage\\\",\\\"azDeploy\\\",\\\"learn\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-vscode.remote-repositories#remoteRepositoriesWalkthrough\\\",{\\\"firstSeen\\\":1705644128994,\\\"stepIDs\\\":[\\\"editCommitRepo\\\",\\\"createGitHubPullRequest\\\",\\\"continueOn\\\",\\\"openRepo\\\",\\\"remoteIndicator\\\"],\\\"manaullyOpened\\\":false}],[\\\"pdconsec.vscode-print#how-to-print\\\",{\\\"firstSeen\\\":1706508194403,\\\"stepIDs\\\":[\\\"print-editor-active\\\",\\\"print-multiline-selection\\\",\\\"print-markdown-rendered\\\",\\\"print-remote\\\"],\\\"manaullyOpened\\\":false}],[\\\"googlecloudtools.cloudcode#apigee-gemini\\\",{\\\"firstSeen\\\":1715068396277,\\\"stepIDs\\\":[\\\"magic_wand_button\\\",\\\"enter_prompt\\\",\\\"api_design_experience\\\",\\\"quick_actions\\\"],\\\"manaullyOpened\\\":false}],[\\\"googlecloudtools.cloudcode#apigee-create-specs\\\",{\\\"firstSeen\\\":1715068396277,\\\"stepIDs\\\":[\\\"connect-signed-out\\\",\\\"connect-signed-in\\\",\\\"enable-ai-companion-signed-out\\\",\\\"enable-ai-companion-not-permitted\\\",\\\"enable-ai-companion-disabled\\\",\\\"enable-ai-companion-enabling\\\",\\\"enable-ai-companion-enabled\\\",\\\"integrate-with-hub-signed-out\\\",\\\"integrate-with-hub-not-permitted\\\",\\\"integrate-with-hub-disabled\\\",\\\"integrate-with-hub-enabling\\\",\\\"integrate-with-hub-enabled\\\",\\\"create-api-specs\\\"],\\\"manaullyOpened\\\":false}],[\\\"googlecloudtools.cloudcode#apigee-create-api-proxies\\\",{\\\"firstSeen\\\":1715068396277,\\\"stepIDs\\\":[\\\"set-up-apigee-workspace-apigee-loading\\\",\\\"set-up-apigee-workspace-no-workspace\\\",\\\"set-up-apigee-workspace-valid-workspace\\\",\\\"build-api-proxies-no-workspace\\\",\\\"build-api-proxies-valid-workspace\\\"],\\\"manaullyOpened\\\":false}],[\\\"amazonwebservices.amazon-q-vscode#aws.amazonq.walkthrough\\\",{\\\"firstSeen\\\":1717483763034,\\\"stepIDs\\\":[\\\"aws.amazonq.walkthrough.chat\\\",\\\"aws.amazonq.walkthrough.inlineSuggestions\\\",\\\"aws.amazonq.walkthrough.securityScan\\\",\\\"aws.amazonq.walkthrough.settings\\\"],\\\"manaullyOpened\\\":false}],[\\\"amazonwebservices.aws-toolkit-vscode#aws.toolkit.lambda.walkthrough\\\",{\\\"firstSeen\\\":1732905114490,\\\"stepIDs\\\":[\\\"toolInstall\\\",\\\"chooseTemplate\\\",\\\"step1\\\",\\\"step2\\\"],\\\"manaullyOpened\\\":false}],[\\\"GitHub.copilot-chat#copilotWelcome\\\",{\\\"firstSeen\\\":1732905114490,\\\"stepIDs\\\":[\\\"copilot.signIn\\\",\\\"copilot.firstSuggest\\\",\\\"copilot.panelChat\\\",\\\"copilot.inlineChatNotMac\\\",\\\"copilot.inlineChatMac\\\",\\\"copilot.iterate\\\",\\\"copilot.creativity\\\"],\\\"manaullyOpened\\\":false}],[\\\"eamodio.gitlens#welcome\\\",{\\\"firstSeen\\\":1737437122874,\\\"stepIDs\\\":[\\\"get-started-community\\\",\\\"welcome-in-trial\\\",\\\"welcome-in-trial-expired\\\",\\\"welcome-in-trial-expired-eligible\\\",\\\"welcome-paid\\\",\\\"visualize-code-history\\\",\\\"accelerate-pr-reviews\\\",\\\"streamline-collaboration\\\",\\\"improve-workflows-with-integrations\\\"],\\\"manaullyOpened\\\":false}],[\\\"googlecloudtools.cloudcode#apigee-create-specs-2\\\",{\\\"firstSeen\\\":1749192395767,\\\"stepIDs\\\":[\\\"connect-signed-out\\\",\\\"connect-signed-in\\\",\\\"enable-ai-companion-signed-out\\\",\\\"enable-ai-companion-not-permitted\\\",\\\"enable-ai-companion-disabled\\\",\\\"enable-ai-companion-enabling\\\",\\\"enable-ai-companion-enabled\\\",\\\"integrate-with-hub-signed-out\\\",\\\"integrate-with-hub-not-provisioned\\\",\\\"integrate-with-hub-provisioned\\\",\\\"integrate-with-hub-provisioned\\\",\\\"create-api-specs\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-azuretools.vscode-containers#containersStart\\\",{\\\"firstSeen\\\":1749192395767,\\\"stepIDs\\\":[\\\"chooseContainerRuntime\\\",\\\"openFolder\\\",\\\"openFolderMac\\\",\\\"scaffold\\\",\\\"buildImage\\\",\\\"runContainer\\\",\\\"containerExplorer\\\",\\\"pushImage\\\",\\\"azDeploy\\\",\\\"learn\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-toolsai.jupyter#jupyterWelcome\\\",{\\\"firstSeen\\\":1749192395767,\\\"stepIDs\\\":[\\\"ipynb.newUntitledIpynb\\\",\\\"jupyter.selectKernel\\\",\\\"jupyter.exploreAndDebug\\\",\\\"jupyter.dataScienceLearnMore\\\"],\\\"manaullyOpened\\\":false}],[\\\"saoudrizwan.claude-dev#ClineWalkthrough\\\",{\\\"firstSeen\\\":1749250000484,\\\"stepIDs\\\":[\\\"welcome\\\",\\\"learn\\\",\\\"advanced-features\\\",\\\"mcp\\\",\\\"getting-started\\\"],\\\"manaullyOpened\\\":false}]]\",\"workbench.view.extension.test.state.hidden\":\"[{\\\"id\\\":\\\"workbench.view.testing\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.view.testCoverage\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.alignment\":\"center\",\"colorThemeData\":\"{\\\"id\\\":\\\"vs-dark vscode-theme-defaults-themes-dark_vs-json\\\",\\\"label\\\":\\\"Dark (Visual Studio)\\\",\\\"settingsId\\\":\\\"Visual Studio Dark\\\",\\\"themeTokenColors\\\":[{\\\"settings\\\":{\\\"foreground\\\":\\\"#D4D4D4\\\"},\\\"scope\\\":[\\\"meta.embedded\\\",\\\"source.groovy.embedded\\\",\\\"string meta.image.inline.markdown\\\",\\\"variable.legacy.builtin.python\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":\\\"emphasis\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\"},\\\"scope\\\":\\\"strong\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#000080\\\"},\\\"scope\\\":\\\"header\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#6A9955\\\"},\\\"scope\\\":\\\"comment\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"constant.language\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#b5cea8\\\"},\\\"scope\\\":[\\\"constant.numeric\\\",\\\"variable.other.enummember\\\",\\\"keyword.operator.plus.exponent\\\",\\\"keyword.operator.minus.exponent\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#646695\\\"},\\\"scope\\\":\\\"constant.regexp\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"entity.name.tag\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d7ba7d\\\"},\\\"scope\\\":[\\\"entity.name.tag.css\\\",\\\"entity.name.tag.less\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9cdcfe\\\"},\\\"scope\\\":\\\"entity.other.attribute-name\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d7ba7d\\\"},\\\"scope\\\":[\\\"entity.other.attribute-name.class.css\\\",\\\"source.css entity.other.attribute-name.class\\\",\\\"entity.other.attribute-name.id.css\\\",\\\"entity.other.attribute-name.parent-selector.css\\\",\\\"entity.other.attribute-name.parent.less\\\",\\\"source.css entity.other.attribute-name.pseudo-class\\\",\\\"entity.other.attribute-name.pseudo-element.css\\\",\\\"source.css.less entity.other.attribute-name.id\\\",\\\"entity.other.attribute-name.scss\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#f44747\\\"},\\\"scope\\\":\\\"invalid\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"underline\\\"},\\\"scope\\\":\\\"markup.underline\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"markup.bold\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"markup.heading\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":\\\"markup.italic\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"strikethrough\\\"},\\\"scope\\\":\\\"markup.strikethrough\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#b5cea8\\\"},\\\"scope\\\":\\\"markup.inserted\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":\\\"markup.deleted\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"markup.changed\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#6A9955\\\"},\\\"scope\\\":\\\"punctuation.definition.quote.begin.markdown\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#6796e6\\\"},\\\"scope\\\":\\\"punctuation.definition.list.begin.markdown\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":\\\"markup.inline.raw\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#808080\\\"},\\\"scope\\\":\\\"punctuation.definition.tag\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"meta.preprocessor\\\",\\\"entity.name.function.preprocessor\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":\\\"meta.preprocessor.string\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#b5cea8\\\"},\\\"scope\\\":\\\"meta.preprocessor.numeric\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9cdcfe\\\"},\\\"scope\\\":\\\"meta.structure.dictionary.key.python\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"meta.diff.header\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"storage\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"storage.type\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"storage.modifier\\\",\\\"keyword.operator.noexcept\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":[\\\"string\\\",\\\"meta.embedded.assembly\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":\\\"string.tag\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":\\\"string.value\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d16969\\\"},\\\"scope\\\":\\\"string.regexp\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"punctuation.definition.template-expression.begin\\\",\\\"punctuation.definition.template-expression.end\\\",\\\"punctuation.section.embedded\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d4d4d4\\\"},\\\"scope\\\":[\\\"meta.template.expression\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9cdcfe\\\"},\\\"scope\\\":[\\\"support.type.vendored.property-name\\\",\\\"support.type.property-name\\\",\\\"source.css variable\\\",\\\"source.coffee.embedded\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"keyword\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"keyword.control\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d4d4d4\\\"},\\\"scope\\\":\\\"keyword.operator\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"keyword.operator.new\\\",\\\"keyword.operator.expression\\\",\\\"keyword.operator.cast\\\",\\\"keyword.operator.sizeof\\\",\\\"keyword.operator.alignof\\\",\\\"keyword.operator.typeid\\\",\\\"keyword.operator.alignas\\\",\\\"keyword.operator.instanceof\\\",\\\"keyword.operator.logical.python\\\",\\\"keyword.operator.wordlike\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#b5cea8\\\"},\\\"scope\\\":\\\"keyword.other.unit\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"punctuation.section.embedded.begin.php\\\",\\\"punctuation.section.embedded.end.php\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9cdcfe\\\"},\\\"scope\\\":\\\"support.function.git-rebase\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#b5cea8\\\"},\\\"scope\\\":\\\"constant.sha.git-rebase\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d4d4d4\\\"},\\\"scope\\\":[\\\"storage.modifier.import.java\\\",\\\"variable.language.wildcard.java\\\",\\\"storage.modifier.package.java\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"variable.language\\\"}],\\\"semanticTokenRules\\\":[{\\\"_selector\\\":\\\"newOperator\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#d4d4d4\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"stringLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ce9178\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"customLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#d4d4d4\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"numberLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#b5cea8\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}}],\\\"extensionData\\\":{\\\"_extensionId\\\":\\\"vscode.theme-defaults\\\",\\\"_extensionIsBuiltin\\\":true,\\\"_extensionName\\\":\\\"theme-defaults\\\",\\\"_extensionPublisher\\\":\\\"vscode\\\"},\\\"themeSemanticHighlighting\\\":true,\\\"colorMap\\\":{\\\"checkbox.border\\\":\\\"#6b6b6b\\\",\\\"editor.background\\\":\\\"#1e1e1e\\\",\\\"editor.foreground\\\":\\\"#d4d4d4\\\",\\\"editor.inactiveSelectionBackground\\\":\\\"#3a3d41\\\",\\\"editorIndentGuide.background1\\\":\\\"#404040\\\",\\\"editorIndentGuide.activeBackground1\\\":\\\"#707070\\\",\\\"editor.selectionHighlightBackground\\\":\\\"#add6ff26\\\",\\\"list.dropBackground\\\":\\\"#383b3d\\\",\\\"activityBarBadge.background\\\":\\\"#007acc\\\",\\\"sideBarTitle.foreground\\\":\\\"#bbbbbb\\\",\\\"input.placeholderForeground\\\":\\\"#a6a6a6\\\",\\\"menu.background\\\":\\\"#252526\\\",\\\"menu.foreground\\\":\\\"#cccccc\\\",\\\"menu.separatorBackground\\\":\\\"#454545\\\",\\\"menu.border\\\":\\\"#454545\\\",\\\"menu.selectionBackground\\\":\\\"#0078d4\\\",\\\"statusBarItem.remoteForeground\\\":\\\"#ffffff\\\",\\\"statusBarItem.remoteBackground\\\":\\\"#16825d\\\",\\\"ports.iconRunningProcessForeground\\\":\\\"#369432\\\",\\\"sideBarSectionHeader.background\\\":\\\"#00000000\\\",\\\"sideBarSectionHeader.border\\\":\\\"#cccccc33\\\",\\\"tab.selectedBackground\\\":\\\"#222222\\\",\\\"tab.selectedForeground\\\":\\\"#ffffffa0\\\",\\\"tab.lastPinnedBorder\\\":\\\"#cccccc33\\\",\\\"list.activeSelectionIconForeground\\\":\\\"#ffffff\\\",\\\"terminal.inactiveSelectionBackground\\\":\\\"#3a3d41\\\",\\\"widget.border\\\":\\\"#303031\\\",\\\"actionBar.toggledBackground\\\":\\\"#383a49\\\"},\\\"watch\\\":false}\",\"terminal.integrated.showTerminalConfigPrompt\":\"false\",\"ces/skipSurvey\":\"1.81.1\",\"fileBasedRecommendations/promptedFileExtensions\":\"[\\\"csv\\\"]\",\"workbench.panel.pinnedPanels\":\"[{\\\"id\\\":\\\"workbench.panel.markers\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":0},{\\\"id\\\":\\\"workbench.panel.output\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":1},{\\\"id\\\":\\\"workbench.panel.repl\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":2},{\\\"id\\\":\\\"terminal\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":3},{\\\"id\\\":\\\"workbench.panel.testResults\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":3},{\\\"id\\\":\\\"~remote.forwardedPortsContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":5},{\\\"id\\\":\\\"workbench.view.extension.gitlensPanel\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":7},{\\\"id\\\":\\\"workbench.view.extension.cspellPanel\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.augment-panel\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":6},{\\\"id\\\":\\\"workbench.view.extension.jupyter-variables\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.panel.comments\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"refactorPreview\\\",\\\"pinned\\\":true,\\\"visible\\\":false}]\",\"cpp.1.lastSessionDate\":\"Tue Jul 15 2025\",\"cpp.1.sessionCount\":\"194\",\"java.2.lastSessionDate\":\"Tue Jul 15 2025\",\"java.2.sessionCount\":\"194\",\"javascript.1.lastSessionDate\":\"Sat Jan 25 2025\",\"javascript.1.sessionCount\":\"95\",\"typescript.1.lastSessionDate\":\"Tue Jul 15 2025\",\"typescript.1.sessionCount\":\"194\",\"csharp.1.lastSessionDate\":\"Tue Jul 15 2025\",\"csharp.1.sessionCount\":\"194\",\"javascript.1.editedCount\":\"10\",\"javascript.1.editedDate\":\"Fri Jan 24 2025\",\"nps/lastSessionDate\":\"Wed Sep 13 2023\",\"nps/sessionCount\":\"9\",\"commandPalette.mru.cache\":\"{\\\"usesLRU\\\":true,\\\"entries\\\":[{\\\"key\\\":\\\"python.createEnvironment\\\",\\\"value\\\":1},{\\\"key\\\":\\\"completion.openUserDict\\\",\\\"value\\\":2},{\\\"key\\\":\\\"dictionary.bing.lookupSelected\\\",\\\"value\\\":6},{\\\"key\\\":\\\"continue.openAccountDialog\\\",\\\"value\\\":24},{\\\"key\\\":\\\"workbench.action.terminal.selectDefaultShell\\\",\\\"value\\\":29},{\\\"key\\\":\\\"_workbench.editSessions.actions.continueEditSession\\\",\\\"value\\\":34},{\\\"key\\\":\\\"github.copilot.generate\\\",\\\"value\\\":51},{\\\"key\\\":\\\"workbench.action.chat.open\\\",\\\"value\\\":53},{\\\"key\\\":\\\"cline.openInNewTab\\\",\\\"value\\\":54},{\\\"key\\\":\\\"extension.comment\\\",\\\"value\\\":58},{\\\"key\\\":\\\"python.setInterpreter\\\",\\\"value\\\":60},{\\\"key\\\":\\\"dendron.renameNote\\\",\\\"value\\\":63},{\\\"key\\\":\\\"dendron.createNewVault\\\",\\\"value\\\":65},{\\\"key\\\":\\\"explorer.newFolder\\\",\\\"value\\\":66},{\\\"key\\\":\\\"dendron.treeView.focus\\\",\\\"value\\\":67},{\\\"key\\\":\\\"workbench.view.extension.cspellPanel\\\",\\\"value\\\":68},{\\\"key\\\":\\\"cline.settingsButtonClicked\\\",\\\"value\\\":69},{\\\"key\\\":\\\"dendron.initWS\\\",\\\"value\\\":76},{\\\"key\\\":\\\"dendron.addExistingVault\\\",\\\"value\\\":77},{\\\"key\\\":\\\"dendron.configureRaw\\\",\\\"value\\\":80},{\\\"key\\\":\\\"workbench.action.reloadWindow\\\",\\\"value\\\":81},{\\\"key\\\":\\\"dendron.reloadIndex\\\",\\\"value\\\":84},{\\\"key\\\":\\\"dendron.lookupNote\\\",\\\"value\\\":87}]}\",\"commandPalette.mru.counter\":\"88\",\"workbench.view.extension.makefile__viewContainer.state.hidden\":\"[{\\\"id\\\":\\\"makefile.outline\\\",\\\"isHidden\\\":false}]\",\"extensionTips/promptedExecutableTips\":\"{\\\"wsl\\\":[\\\"ms-vscode-remote.remote-wsl\\\"],\\\"docker\\\":[\\\"ms-azuretools.vscode-docker\\\",\\\"ms-vscode-remote.remote-containers\\\"]}\",\"workbench.view.remote.state.hidden\":\"[{\\\"id\\\":\\\"targetsWsl\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"targetsContainers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"detailsContainers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"devVolumes\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"remoteHub.views.workspaceRepositories\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"~remote.helpPanel\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.vscode-openai-sidebar-view.state.hidden\":\"[{\\\"id\\\":\\\"vscode-openai.conversations.view.sidebar\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-openai.embeddings.view.sidebar\\\",\\\"isHidden\\\":false}]\",\"nps/isCandidate\":\"false\",\"nps/skipVersion\":\"1.81.1\",\"~remote.forwardedPortsContainer.hidden\":\"[{\\\"id\\\":\\\"~remote.forwardedPorts\\\",\\\"isHidden\\\":false}]\",\"github-jamesmiller2404\":\"[{\\\"id\\\":\\\"andrewbutson.vscode-openai\\\",\\\"name\\\":\\\"vscode-openai\\\",\\\"allowed\\\":true},{\\\"id\\\":\\\"vscode.tunnel-forwarding\\\",\\\"name\\\":\\\"Local Tunnel Port Forwarding\\\",\\\"allowed\\\":true},{\\\"id\\\":\\\"vscode.github\\\",\\\"name\\\":\\\"GitHub\\\",\\\"allowed\\\":true},{\\\"id\\\":\\\"continue.continue\\\",\\\"name\\\":\\\"Continue - Codestral, Claude, and more\\\",\\\"allowed\\\":true}]\",\"remote.tunnels.toRestore.undefined.-1334945669\":\"[{\\\"remoteHost\\\":\\\"localhost\\\",\\\"remotePort\\\":3000,\\\"closeable\\\":true,\\\"localAddress\\\":\\\"https://dp68wqc9-3000.usw3.devtunnels.ms/\\\",\\\"protocol\\\":\\\"http\\\",\\\"localUri\\\":{\\\"$mid\\\":1,\\\"path\\\":\\\"/\\\",\\\"scheme\\\":\\\"https\\\",\\\"authority\\\":\\\"dp68wqc9-3000.usw3.devtunnels.ms\\\"},\\\"hasRunningProcess\\\":false,\\\"source\\\":{\\\"source\\\":0,\\\"description\\\":\\\"User Forwarded\\\"},\\\"privacy\\\":\\\"private\\\"}]\",\"workbench.panel.chatSidebar.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.chat.view.copilot\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.chat.movedView.welcomeView\\\",\\\"isHidden\\\":false}]\",\"memento/notebookGettingStarted2\":\"{\\\"hasOpenedNotebook\\\":true}\",\"workbench.activityBar.location\":\"default\",\"tabs-list-width-horizontal\":\"173\",\"workbench.view.extension.dockerView.state.hidden\":\"[{\\\"id\\\":\\\"dockerContainers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerImages\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerRegistries\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerNetworks\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerVolumes\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-docker.views.dockerContexts\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-docker.views.help\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.kubernetesView.state.hidden\":\"[{\\\"id\\\":\\\"extension.vsKubernetesExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"extension.vsKubernetesHelmRepoExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"kubernetes.cloudExplorer\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.1-cloudCodeContainer.state.hidden\":\"[{\\\"id\\\":\\\"cloudcode.unified.kubernetes.localDevExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cloudcode.unified.kubectlExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cloudcode.unified.cloudRunExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cloudcode.unified.apiExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cloudcode.unified.secretsExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cloudcode.unified.gceExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cloudcode.unified.apigee.explorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cloudcode.unified.gcfExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cloudcode.unified.awsLambdaImporterExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cloudcode.unified.cloudStorageExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cloudcode.unified.sourceProtectExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cloudcode.unified.mcdcSources\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cloudcode.helpAndFeedbackExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cloudcode.unified.dataprocExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cloudcode.unified.notebookExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cloudcode.unified.bigQueryDatasetExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cloudcode.unified.gcsExplorer\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.1-cloudCodeDuetAIChatViewContainer.state.hidden\":\"[{\\\"id\\\":\\\"cloudcode.duetAI.chatView\\\",\\\"isHidden\\\":false}]\",\"typescript.1.editedCount\":\"1\",\"typescript.1.editedDate\":\"Sun Mar 24 2024\",\"workbench.view.extension.aws-explorer.state.hidden\":\"[{\\\"id\\\":\\\"aws.amazonq.codewhisperer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"aws.explorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"aws.cdk\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"aws.codecatalyst\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"aws.toolkit.AmazonCommonAuth\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"aws.toolkit.notifications\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"aws.appBuilder\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.amazonq.state.hidden\":\"[{\\\"id\\\":\\\"aws.amazonq.AmazonCommonAuth\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"aws.AmazonQChatView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"aws.AmazonQNeverShowBadge\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"aws.amazonq.notifications\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"aws.amazonq.SecurityIssuesTree\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.aws-codewhisperer-reference-log.state.hidden\":\"[{\\\"id\\\":\\\"aws.codeWhisperer.referenceLog\\\",\\\"isHidden\\\":false}]\",\"workbench.auxiliarybar.pinnedPanels\":\"[{\\\"id\\\":\\\"workbench.panel.chatEditing\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":101},{\\\"id\\\":\\\"workbench.panel.chat\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":100}]\",\"workbench.panel.chat.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.chat.view.copilot\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"continue.continueGUIView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"augment-chat\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.chatEditing.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.chat.view.edits\\\",\\\"isHidden\\\":false}]\",\"workbench.welcomePage.hiddenCategories\":\"[\\\"ms-vscode-remote.remote-wsl#wslWalkthrough\\\",\\\"GitHub.copilot-chat#copilotWelcome\\\"]\",\"workbench.view.extension.continue.state.hidden\":\"[{\\\"id\\\":\\\"continue.continueGUIView\\\",\\\"isHidden\\\":false}]\",\"views.customizations\":\"{\\\"viewContainerLocations\\\":{},\\\"viewLocations\\\":{\\\"continue.continueGUIView\\\":\\\"workbench.panel.chat\\\",\\\"augment-chat\\\":\\\"workbench.panel.chat\\\"},\\\"viewContainerBadgeEnablementStates\\\":{}}\",\"workbench.view.extension.gitlensInspect.state.hidden\":\"[{\\\"id\\\":\\\"gitlens.views.commitDetails\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.pullRequest\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.lineHistory\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.fileHistory\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.timeline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.searchAndCompare\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.gitlens.state.hidden\":\"[{\\\"id\\\":\\\"gitlens.views.home\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.launchpad\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.drafts\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.workspaces\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.gitlensPanel.state.hidden\":\"[{\\\"id\\\":\\\"gitlens.views.graph\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.graphDetails\\\",\\\"isHidden\\\":false}]\",\"Comments.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.comments\\\",\\\"isHidden\\\":false}]\",\"extension.features.state\":\"{\\\"github.copilot-chat\\\":{\\\"copilot\\\":{\\\"disabled\\\":false,\\\"accessTimes\\\":[1752472496575,1752472498349,1752472508790,1752611853449]}}}\",\"languageModelAccess.gpt-4o-mini\":\"[\\\"github.copilot-chat\\\"]\",\"languageModelStats.gpt-4o-mini\":\"{\\\"extensions\\\":[{\\\"extensionId\\\":\\\"GitHub.copilot-chat\\\",\\\"requestCount\\\":29,\\\"tokenCount\\\":275129,\\\"participants\\\":[]}]}\",\"languageModelAccess.gpt-4o\":\"[\\\"github.copilot-chat\\\"]\",\"languageModelStats.gpt-4o\":\"{\\\"extensions\\\":[{\\\"extensionId\\\":\\\"GitHub.copilot-chat\\\",\\\"requestCount\\\":31,\\\"tokenCount\\\":537489,\\\"participants\\\":[]}]}\",\"workbench.view.extension.claude-dev-ActivityBar.state.hidden\":\"[{\\\"id\\\":\\\"claude-dev.SidebarProvider\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.references-view.state.hidden\":\"[{\\\"id\\\":\\\"references-view.tree\\\",\\\"isHidden\\\":false}]\",\"javascript.1.isCandidate\":\"false\",\"javascript.1.skipVersion\":\"1.96.4\",\"extensions.trustedPublishers\":\"{\\\"andrewbutson\\\":{\\\"publisher\\\":\\\"andrewbutson\\\",\\\"publisherDisplayName\\\":\\\"Andrew Butson\\\"},\\\"compulim\\\":{\\\"publisher\\\":\\\"compulim\\\",\\\"publisherDisplayName\\\":\\\"Compulim\\\"},\\\"continue\\\":{\\\"publisher\\\":\\\"continue\\\",\\\"publisherDisplayName\\\":\\\"Continue\\\"},\\\"eamodio\\\":{\\\"publisher\\\":\\\"eamodio\\\",\\\"publisherDisplayName\\\":\\\"GitKraken\\\"},\\\"google\\\":{\\\"publisher\\\":\\\"google\\\",\\\"publisherDisplayName\\\":\\\"Google\\\"},\\\"googlecloudtools\\\":{\\\"publisher\\\":\\\"googlecloudtools\\\",\\\"publisherDisplayName\\\":\\\"Google Cloud\\\"},\\\"mechatroner\\\":{\\\"publisher\\\":\\\"mechatroner\\\",\\\"publisherDisplayName\\\":\\\"mechatroner\\\"},\\\"pdconsec\\\":{\\\"publisher\\\":\\\"pdconsec\\\",\\\"publisherDisplayName\\\":\\\"PD Consulting\\\"},\\\"rassek96\\\":{\\\"publisher\\\":\\\"rassek96\\\",\\\"publisherDisplayName\\\":\\\"rassek96\\\"},\\\"redhat\\\":{\\\"publisher\\\":\\\"redhat\\\",\\\"publisherDisplayName\\\":\\\"Red Hat\\\"},\\\"saoudrizwan\\\":{\\\"publisher\\\":\\\"saoudrizwan\\\",\\\"publisherDisplayName\\\":\\\"Cline\\\"},\\\"yzhang\\\":{\\\"publisher\\\":\\\"yzhang\\\",\\\"publisherDisplayName\\\":\\\"Yu Zhang\\\"},\\\"james-ni\\\":{\\\"publisher\\\":\\\"James-Ni\\\",\\\"publisherDisplayName\\\":\\\"James-Ni\\\"},\\\"streetsidesoftware\\\":{\\\"publisher\\\":\\\"streetsidesoftware\\\",\\\"publisherDisplayName\\\":\\\"Street Side Software\\\"},\\\"augment\\\":{\\\"publisher\\\":\\\"augment\\\",\\\"publisherDisplayName\\\":\\\"Augment Computing\\\"},\\\"docker\\\":{\\\"publisher\\\":\\\"docker\\\",\\\"publisherDisplayName\\\":\\\"Docker\\\"},\\\"tomoki1207\\\":{\\\"publisher\\\":\\\"tomoki1207\\\",\\\"publisherDisplayName\\\":\\\"tomoki1207\\\"},\\\"dendron\\\":{\\\"publisher\\\":\\\"dendron\\\",\\\"publisherDisplayName\\\":\\\"dendron\\\"},\\\"shahilkumar\\\":{\\\"publisher\\\":\\\"ShahilKumar\\\",\\\"publisherDisplayName\\\":\\\"Shahil Kumar\\\"},\\\"bierner\\\":{\\\"publisher\\\":\\\"bierner\\\",\\\"publisherDisplayName\\\":\\\"Matt Bierner\\\"},\\\"davidanson\\\":{\\\"publisher\\\":\\\"DavidAnson\\\",\\\"publisherDisplayName\\\":\\\"David Anson\\\"},\\\"johnpapa\\\":{\\\"publisher\\\":\\\"johnpapa\\\",\\\"publisherDisplayName\\\":\\\"John Papa\\\"},\\\"oliverkovacs\\\":{\\\"publisher\\\":\\\"OliverKovacs\\\",\\\"publisherDisplayName\\\":\\\"OliverKovacs\\\"},\\\"ban\\\":{\\\"publisher\\\":\\\"ban\\\",\\\"publisherDisplayName\\\":\\\"Bartosz Antosik\\\"}}\",\"chat.currentLanguageModel.panel\":\"github.copilot-chat/claude-3.5-sonnet\",\"workbench.view.extension.cspellPanel.state.hidden\":\"[{\\\"id\\\":\\\"cSpellIssuesViewByFile\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cSpellIssuesViewByIssue\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.augment-chat.state.hidden\":\"[{\\\"id\\\":\\\"augment-chat\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.augment-panel.state.hidden\":\"[{\\\"id\\\":\\\"augment-next-edit\\\",\\\"isHidden\\\":false}]\",\"scm.input.lastActionId\":\"github.copilot.git.generateCommitMessage\",\"chatEditsView.hideMovedEditsView\":\"true\",\"chat.currentLanguageModel.editor\":\"github.copilot-chat/gpt-4.1\",\"chat.currentLanguageModel.panel.isDefault\":\"false\",\"workbench.view.extension.geminiChat.state.hidden\":\"[{\\\"id\\\":\\\"cloudcode.gemini.chatView\\\",\\\"isHidden\\\":false}]\",\"chat.currentLanguageModel.notebook\":\"github.copilot-chat/gpt-4.1\",\"chat.currentLanguageModel.notebook.isDefault\":\"true\",\"workbench.view.extension.containersView.state.hidden\":\"[{\\\"id\\\":\\\"vscode-containers.views.containers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-containers.views.images\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-containers.views.registries\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-containers.views.networks\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-containers.views.volumes\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-containers.views.dockerContexts\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-containers.views.help\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.dendron-view.state.hidden\":\"[{\\\"id\\\":\\\"dendron.tip-of-the-day\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dendron.backlinks\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dendron.treeView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dendron.lookup-view\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dendron.calendar-view\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dendron.recent-workspaces\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dendron.help-and-feedback\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dendron.graph-panel\\\",\\\"isHidden\\\":false}]\",\"chat.lastChatMode\":\"ask\",\"chat.currentLanguageModel.editor.isDefault\":\"true\"}}"}